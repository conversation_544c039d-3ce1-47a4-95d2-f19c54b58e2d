<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import type { Term } from '$lib/types/tables.js';

  export let terms: Term[] = [];
  export let loading = false;
  export let onAdd: (term: Partial<Term>) => Promise<void>;
  export let onUpdate: (term: Partial<Term>) => Promise<void>;
  export let onDelete: (id: number) => Promise<void>;

  // Pagination and search states
  let itemsPerPage = 40;
  let currentPage = 1;
  let searchQuery = '';

  // Form states
  let showAddDialog = false;
  let showEditDialog = false;

  // Form data
  let newTerm: Partial<Term> = {
    term_name: '',
    term_slug: '',
    term_taxonomy: ''
  };
  let editTerm: Partial<Term> = {};

  // Filtering and pagination functions
  function filterTerms() {
    if (!terms || !Array.isArray(terms)) return [];
    // Only filter if search query is more than 2 characters
    if (!searchQuery || searchQuery.length <= 2) return terms;
    const query = searchQuery.toLowerCase();
    return terms.filter(term => 
      term.term_name?.toLowerCase().includes(query) ||
      term.term_slug?.toLowerCase().includes(query) ||
      term.term_taxonomy?.toLowerCase().includes(query)
    );
  }

  function paginateData(data: Term[]) {
    if (!data || !Array.isArray(data)) return [];
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    return data.slice(start, end);
  }

  function getTotalPages(dataLength: number) {
    return Math.ceil(dataLength / itemsPerPage);
  }

  function changePage(page: number) {
    currentPage = page;
  }

  function openEditTerm(term: Term) {
    editTerm = { ...term };
    showEditDialog = true;
  }

  async function addTerm() {
    try {
      await onAdd(newTerm);
      newTerm = {
        term_name: '',
        term_slug: '',
        term_taxonomy: ''
      };
      showAddDialog = false;
    } catch (error) {
      console.error('Failed to add term:', error);
      alert('Failed to add term');
    }
  }

  async function updateTerm() {
    try {
      await onUpdate(editTerm);
      showEditDialog = false;
    } catch (error) {
      console.error('Failed to update term:', error);
      alert('Failed to update term');
    }
  }

  async function deleteTerm(id: number) {
    if (confirm('Are you sure you want to delete this term?')) {
      try {
        await onDelete(id);
      } catch (error) {
        console.error('Failed to delete term:', error);
        alert('Failed to delete term');
      }
    }
  }

  // Reactive statements for filtered and paginated data
  $: filteredTerms = filterTerms();
  $: paginatedTerms = paginateData(filteredTerms);
  $: totalPages = getTotalPages(filteredTerms.length);
</script>

<div class="space-y-4">
  <div class="flex justify-between items-center">
    <h2 class="text-xl font-semibold">Terms Management</h2>
    <button
      onclick={() => showAddDialog = true}
      class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 transition-colors text-sm font-medium"
    >
      Add New Term
    </button>
  </div>

  <!-- Search Box -->
  <div class="flex gap-4 items-center">
    <div class="flex-1">
      <input
        type="text"
        placeholder="Search terms by name, slug, or taxonomy (min 3 characters)..."
        bind:value={searchQuery}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    <div class="text-sm text-gray-600">
      {searchQuery && searchQuery.length > 2 ? `${filteredTerms?.length || 0} of ${terms?.length || 0}` : terms?.length || 0} terms
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {#each paginatedTerms as term}
      <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
        <div class="p-6">
          <div class="space-y-3">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="font-semibold text-lg text-gray-900 mb-1">{term.term_name}</h3>
                <p class="text-sm text-gray-600">Slug: {term.term_slug}</p>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                ID: {term.term_id}
              </span>
            </div>

            <div class="space-y-1">
              <div class="flex items-center gap-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  {term.term_taxonomy}
                </span>
              </div>
            </div>

            <div class="flex gap-2 pt-2">
              <button 
                onclick={() => openEditTerm(term)}
                class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
              >
                Edit
              </button>
              <button
                onclick={() => deleteTerm(term.term_id!)}
                class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    {/each}
  </div>

  <!-- Pagination -->
  {#if totalPages > 1}
    <div class="flex justify-center items-center gap-4 mt-6">
      <button
        onclick={() => changePage(currentPage - 1)}
        disabled={currentPage === 1}
        class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
      >
        Previous
      </button>
      
      <span class="text-gray-600">
        Page {currentPage} of {totalPages}
      </span>
      
      <button
        onclick={() => changePage(currentPage + 1)}
        disabled={currentPage === totalPages}
        class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
      >
        Next
      </button>
    </div>
  {/if}
</div>

<!-- Add Term Dialog -->
<Dialog.Root bind:open={showAddDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Add New Term</Dialog.Title>
        <Dialog.Description>Create a new term.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="term_name" class="text-right text-sm font-medium">Name</label>
          <input
            id="term_name"
            type="text"
            bind:value={newTerm.term_name}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Term name"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="term_slug" class="text-right text-sm font-medium">Slug</label>
          <input
            id="term_slug"
            type="text"
            bind:value={newTerm.term_slug}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="term-slug"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="term_taxonomy" class="text-right text-sm font-medium">Taxonomy</label>
          <input
            id="term_taxonomy"
            type="text"
            bind:value={newTerm.term_taxonomy}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="category, tag, etc."
          />
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showAddDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={addTerm}
          disabled={!newTerm.term_name || !newTerm.term_slug || !newTerm.term_taxonomy}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Add Term
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

<!-- Edit Term Dialog -->
<Dialog.Root bind:open={showEditDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Edit Term</Dialog.Title>
        <Dialog.Description>Update term information.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_term_name" class="text-right text-sm font-medium">Name</label>
          <input
            id="edit_term_name"
            type="text"
            bind:value={editTerm.term_name}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Term name"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_term_slug" class="text-right text-sm font-medium">Slug</label>
          <input
            id="edit_term_slug"
            type="text"
            bind:value={editTerm.term_slug}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="term-slug"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_term_taxonomy" class="text-right text-sm font-medium">Taxonomy</label>
          <input
            id="edit_term_taxonomy"
            type="text"
            bind:value={editTerm.term_taxonomy}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="category, tag, etc."
          />
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showEditDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={updateTerm}
          disabled={!editTerm.term_name || !editTerm.term_slug || !editTerm.term_taxonomy}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Update Term
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
