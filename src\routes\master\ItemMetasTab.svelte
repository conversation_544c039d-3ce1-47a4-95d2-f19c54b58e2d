<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import type { ItemMeta, Item } from '$lib/types/tables.js';

  export let itemMetas: ItemMeta[] = [];
  export let items: Item[] = [];
  export let loading = false;
  export let onAdd: (itemMeta: Partial<ItemMeta>) => Promise<void>;
  export let onUpdate: (itemMeta: Partial<ItemMeta>) => Promise<void>;
  export let onDelete: (id: number) => Promise<void>;

  // Pagination and search states
  let itemsPerPage = 40;
  let currentPage = 1;
  let searchQuery = '';

  // Form states
  let showAddDialog = false;
  let showEditDialog = false;

  // Form data
  let newItemMeta: Partial<ItemMeta> = {
    item_id: undefined,
    item_meta_key: '',
    item_meta_value: ''
  };
  let editItemMeta: Partial<ItemMeta> = {};

  // Filtering and pagination functions
  function filterItemMetas() {
    if (!itemMetas || !Array.isArray(itemMetas)) return [];
    // Only filter if search query is more than 2 characters
    if (!searchQuery || searchQuery.length <= 2) return itemMetas;
    const query = searchQuery.toLowerCase();
    return itemMetas.filter(meta => 
      meta.item_meta_key?.toLowerCase().includes(query) ||
      meta.item_meta_value?.toLowerCase().includes(query)
    );
  }

  function paginateData(data: ItemMeta[]) {
    if (!data || !Array.isArray(data)) return [];
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    return data.slice(start, end);
  }

  function getTotalPages(dataLength: number) {
    return Math.ceil(dataLength / itemsPerPage);
  }

  function changePage(page: number) {
    currentPage = page;
  }

  function openEditItemMeta(itemMeta: ItemMeta) {
    editItemMeta = { ...itemMeta };
    showEditDialog = true;
  }

  async function addItemMeta() {
    try {
      await onAdd(newItemMeta);
      newItemMeta = {
        item_id: undefined,
        item_meta_key: '',
        item_meta_value: ''
      };
      showAddDialog = false;
    } catch (error) {
      console.error('Failed to add item meta:', error);
      alert('Failed to add item meta');
    }
  }

  async function updateItemMeta() {
    try {
      await onUpdate(editItemMeta);
      showEditDialog = false;
    } catch (error) {
      console.error('Failed to update item meta:', error);
      alert('Failed to update item meta');
    }
  }

  async function deleteItemMeta(id: number) {
    if (confirm('Are you sure you want to delete this item meta?')) {
      try {
        await onDelete(id);
      } catch (error) {
        console.error('Failed to delete item meta:', error);
        alert('Failed to delete item meta');
      }
    }
  }

  // Reactive statements for filtered and paginated data
  $: filteredItemMetas = filterItemMetas();
  $: paginatedItemMetas = paginateData(filteredItemMetas);
  $: totalPages = getTotalPages(filteredItemMetas.length);
</script>

<div class="space-y-4">
  <div class="flex justify-between items-center">
    <h2 class="text-xl font-semibold">Item Metas Management</h2>
    <button
      onclick={() => showAddDialog = true}
      disabled={!items || items.length === 0}
      title={!items || items.length === 0 ? "Create an item first before adding item metas" : ""}
      class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
    >
      Add New Item Meta
    </button>
  </div>

  <!-- Search Box -->
  <div class="flex gap-4 items-center">
    <div class="flex-1">
      <input
        type="text"
        placeholder="Search item metas by key or value (min 3 characters)..."
        bind:value={searchQuery}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    <div class="text-sm text-gray-600">
      {searchQuery && searchQuery.length > 2 ? `${filteredItemMetas?.length || 0} of ${itemMetas?.length || 0}` : itemMetas?.length || 0} item metas
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {#each paginatedItemMetas as itemMeta}
      <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
        <div class="p-6">
          <div class="space-y-3">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="font-semibold text-lg text-gray-900 mb-1">{itemMeta.item_meta_key}</h3>
                <p class="text-sm text-gray-600 break-all">{itemMeta.item_meta_value || 'No value'}</p>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                ID: {itemMeta.item_meta_id}
              </span>
            </div>

            <div class="space-y-1">
              <p class="text-xs text-gray-500">Item ID: {itemMeta.item_id}</p>
            </div>

            <div class="flex gap-2 pt-2">
              <button 
                onclick={() => openEditItemMeta(itemMeta)}
                class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
              >
                Edit
              </button>
              <button
                onclick={() => deleteItemMeta(itemMeta.item_meta_id!)}
                class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    {/each}
  </div>

  <!-- Pagination -->
  {#if totalPages > 1}
    <div class="flex justify-center items-center gap-4 mt-6">
      <button
        onclick={() => changePage(currentPage - 1)}
        disabled={currentPage === 1}
        class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
      >
        Previous
      </button>
      
      <span class="text-gray-600">
        Page {currentPage} of {totalPages}
      </span>
      
      <button
        onclick={() => changePage(currentPage + 1)}
        disabled={currentPage === totalPages}
        class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
      >
        Next
      </button>
    </div>
  {/if}
</div>

<!-- Add Item Meta Dialog -->
<Dialog.Root bind:open={showAddDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Add New Item Meta</Dialog.Title>
        <Dialog.Description>Create a new item meta.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="item_meta_item_id" class="text-right text-sm font-medium">Item</label>
          <select
            id="item_meta_item_id"
            bind:value={newItemMeta.item_id}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select an item (required)</option>
            {#each items as item}
              <option value={item.item_id}>{item.item_name} (ID: {item.item_id})</option>
            {/each}
          </select>
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="item_meta_key" class="text-right text-sm font-medium">Key</label>
          <input
            id="item_meta_key"
            type="text"
            bind:value={newItemMeta.item_meta_key}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="meta_key"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="item_meta_value" class="text-right text-sm font-medium">Value</label>
          <input
            id="item_meta_value"
            type="text"
            bind:value={newItemMeta.item_meta_value}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Meta value"
          />
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showAddDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={addItemMeta}
          disabled={!newItemMeta.item_meta_key || !newItemMeta.item_id}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Add Item Meta
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

<!-- Edit Item Meta Dialog -->
<Dialog.Root bind:open={showEditDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Edit Item Meta</Dialog.Title>
        <Dialog.Description>Update item meta information.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_item_meta_item_id" class="text-right text-sm font-medium">Item</label>
          <select
            id="edit_item_meta_item_id"
            bind:value={editItemMeta.item_id}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select an item (required)</option>
            {#each items as item}
              <option value={item.item_id}>{item.item_name} (ID: {item.item_id})</option>
            {/each}
          </select>
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_item_meta_key" class="text-right text-sm font-medium">Key</label>
          <input
            id="edit_item_meta_key"
            type="text"
            bind:value={editItemMeta.item_meta_key}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="meta_key"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_item_meta_value" class="text-right text-sm font-medium">Value</label>
          <input
            id="edit_item_meta_value"
            type="text"
            bind:value={editItemMeta.item_meta_value}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Meta value"
          />
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showEditDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={updateItemMeta}
          disabled={!editItemMeta.item_meta_key || !editItemMeta.item_id}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Update Item Meta
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
