<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import type { User } from '$lib/types/tables.js';

  export let users: User[] = [];
  export let onAdd: (user: Partial<User>) => Promise<void>;
  export let onUpdate: (user: Partial<User>) => Promise<void>;
  export let onDelete: (id: number) => Promise<void>;

  // Pagination and search states
  let itemsPerPage = 40;
  let currentPage = 1;
  let searchQuery = '';

  // Form states
  let showAddDialog = false;
  let showEditDialog = false;

  // Form data
  let newUser: Partial<User> = {
    user_firstname: '',
    user_email: '',
    user_type: 1
  };
  let editUser: Partial<User> = {};

  // Filtering and pagination functions
  function filterUsers() {
    if (!users || !Array.isArray(users)) return [];
    // Only filter if search query is more than 2 characters
    if (!searchQuery || searchQuery.length <= 2) return users;
    const query = searchQuery.toLowerCase();
    return users.filter(user => 
      user.user_firstname?.toLowerCase().includes(query) ||
      user.user_email?.toLowerCase().includes(query)
    );
  }

  function paginateData(data: User[]) {
    if (!data || !Array.isArray(data)) return [];
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    return data.slice(start, end);
  }

  function getTotalPages(dataLength: number) {
    return Math.ceil(dataLength / itemsPerPage);
  }

  function changePage(page: number) {
    currentPage = page;
  }

  function openEditUser(user: User) {
    editUser = { ...user };
    showEditDialog = true;
  }

  async function addUser() {
    try {
      await onAdd(newUser);
      newUser = {
        user_firstname: '',
        user_email: '',
        user_type: 1
      };
      showAddDialog = false;
    } catch (error) {
      console.error('Failed to add user:', error);
      alert('Failed to add user');
    }
  }

  async function updateUser() {
    try {
      await onUpdate(editUser);
      showEditDialog = false;
    } catch (error) {
      console.error('Failed to update user:', error);
      alert('Failed to update user');
    }
  }

  async function deleteUser(id: number) {
    if (confirm('Are you sure you want to delete this user?')) {
      try {
        await onDelete(id);
      } catch (error) {
        console.error('Failed to delete user:', error);
        alert('Failed to delete user');
      }
    }
  }

  // Reactive statements for filtered and paginated data
  $: filteredUsers = filterUsers();
  $: paginatedUsers = paginateData(filteredUsers);
  $: totalPages = getTotalPages(filteredUsers.length);
</script>

<div class="space-y-4">
  <div class="flex justify-between items-center">
    <h2 class="text-xl font-semibold">Users Management</h2>
    <button
      onclick={() => showAddDialog = true}
      class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 transition-colors text-sm font-medium"
    >
      Add New User
    </button>
  </div>

  <!-- Search Box -->
  <div class="flex gap-4 items-center">
    <div class="flex-1">
      <input
        type="text"
        placeholder="Search users by name or email (min 3 characters)..."
        bind:value={searchQuery}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    <div class="text-sm text-gray-600">
      {searchQuery && searchQuery.length > 2 ? `${filteredUsers?.length || 0} of ${users?.length || 0}` : users?.length || 0} users
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {#each paginatedUsers as user}
      <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
        <div class="p-6">
          <div class="space-y-3">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="font-semibold text-lg text-gray-900 mb-1">{user.user_firstname}</h3>
                <p class="text-sm text-gray-600 break-all">{user.user_email}</p>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                ID: {user.user_id}
              </span>
            </div>

            <div class="space-y-1">
              <div class="flex items-center gap-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {user.user_type === 0 ? 'Inactive' : user.user_type === 1 ? 'Member' : 'Admin'}
                </span>
              </div>
              <p class="text-xs text-gray-500">
                Created: {new Date(Number(user.user_created_at)).toLocaleDateString()}
              </p>
            </div>

            <div class="flex gap-2 pt-2">
              <button 
                onclick={() => openEditUser(user)}
                class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
              >
                Edit
              </button>
              <button
                onclick={() => deleteUser(user.user_id!)}
                class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    {/each}
  </div>

  <!-- Pagination -->
  {#if totalPages > 1}
    <div class="flex justify-center items-center gap-4 mt-6">
      <button
        onclick={() => changePage(currentPage - 1)}
        disabled={currentPage === 1}
        class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
      >
        Previous
      </button>
      
      <span class="text-gray-600">
        Page {currentPage} of {totalPages}
      </span>
      
      <button
        onclick={() => changePage(currentPage + 1)}
        disabled={currentPage === totalPages}
        class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
      >
        Next
      </button>
    </div>
  {/if}
</div>

<!-- Add User Dialog -->
<Dialog.Root bind:open={showAddDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Add New User</Dialog.Title>
        <Dialog.Description>Create a new user account.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="user_firstname" class="text-right text-sm font-medium">Name</label>
          <input
            id="user_firstname"
            type="text"
            bind:value={newUser.user_firstname}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="First name"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="user_email" class="text-right text-sm font-medium">Email</label>
          <input
            id="user_email"
            type="email"
            bind:value={newUser.user_email}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="<EMAIL>"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="user_type" class="text-right text-sm font-medium">Type</label>
          <select
            id="user_type"
            bind:value={newUser.user_type}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={0}>Inactive</option>
            <option value={1}>Member</option>
            <option value={2}>Admin</option>
          </select>
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showAddDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={addUser}
          disabled={!newUser.user_firstname || !newUser.user_email}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Add User
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

<!-- Edit User Dialog -->
<Dialog.Root bind:open={showEditDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Edit User</Dialog.Title>
        <Dialog.Description>Update user information.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_user_firstname" class="text-right text-sm font-medium">Name</label>
          <input
            id="edit_user_firstname"
            type="text"
            bind:value={editUser.user_firstname}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="First name"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_user_email" class="text-right text-sm font-medium">Email</label>
          <input
            id="edit_user_email"
            type="email"
            bind:value={editUser.user_email}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="<EMAIL>"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_user_type" class="text-right text-sm font-medium">Type</label>
          <select
            id="edit_user_type"
            bind:value={editUser.user_type}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={0}>Inactive</option>
            <option value={1}>Member</option>
            <option value={2}>Admin</option>
          </select>
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showEditDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={updateUser}
          disabled={!editUser.user_firstname || !editUser.user_email}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Update User
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
