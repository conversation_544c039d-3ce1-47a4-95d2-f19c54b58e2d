<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';

  // Import Dialog component
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import type { User, Item, Term, Option, ItemMeta, TermMeta } from '$lib/types/tables.js';

  let isAuthenticated = false;
  let password = '';
  let token = '';
  let loginError = '';

  let users: User[] = [];
  let items: Item[] = [];
  let terms: Term[] = [];
  let options: Option[] = [];
  let itemMetas: ItemMeta[] = [];
  let termMetas: TermMeta[] = [];

  let loading = false;
  let activeTab = 'users';
  let dataError = '';
  let showSetupMessage = false;

  // Pagination and search states
  let itemsPerPage = 40;
  let currentPage = {
    users: 1,
    items: 1,
    terms: 1,
    options: 1,
    'item-metas': 1,
    'term-metas': 1
  };
  let searchQuery = {
    users: '',
    items: '',
    terms: '',
    options: '',
    'item-metas': '',
    'term-metas': ''
  };
  let itemStatusFilter = ''; // For items tab: '', '0', '1', '2'

  // Form states
  let showAddUserDialog = false;
  let showAddItemDialog = false;
  let showAddTermDialog = false;
  let showAddOptionDialog = false;
  let showAddItemMetaDialog = false;
  let showAddTermMetaDialog = false;

  // Edit dialog states
  let showEditUserDialog = false;
  let showEditItemDialog = false;
  let showEditTermDialog = false;
  let showEditOptionDialog = false;
  let showEditItemMetaDialog = false;
  let showEditTermMetaDialog = false;

  // Edit form data
  let editUser: Partial<User> = {};
  let editItem: Partial<Item> = {};
  let editTerm: Partial<Term> = {};
  let editOption: Partial<Option> = {};
  let editItemMeta: Partial<ItemMeta> = {};
  let editTermMeta: Partial<TermMeta> = {};
  let newUser: Partial<User> = {
    user_firstname: '',
    user_email: '',
    user_created_at: Date.now(),
    user_type: 1
  };
  let newItem: Partial<Item> = {
    item_name: '',
    item_slug: '',
    item_status: 0,
    item_created_at: Date.now(),
    item_url: '',
    user_id: undefined
  };
  let newTerm: Partial<Term> = {
    term_name: '',
    term_slug: '',
    term_taxonomy: ''
  };
  let newOption: Partial<Option> = {
    option_key: '',
    option_value: ''
  };
  let newItemMeta: Partial<ItemMeta> = {
    item_id: undefined,
    item_meta_key: '',
    item_meta_value: ''
  };
  let newTermMeta: Partial<TermMeta> = {
    term_id: undefined,
    term_meta_key: '',
    term_meta_value: ''
  };
  
  onMount(() => {
    if (browser) {
      const savedToken = localStorage.getItem('master_token');
      if (savedToken) {
        token = savedToken;
        isAuthenticated = true;
        loadData();
      }
    }
  });
  
  async function login() {
    try {
      loading = true;
      loginError = '';
      
      const response = await fetch('/api/Login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ password })
      });
      
      const result = await response.json();
      
      if (result.success) {
        token = result.data.token;
        isAuthenticated = true;
        localStorage.setItem('master_token', token);
        await loadData();
      } else {
        loginError = result.error || 'Login failed';
      }
    } catch (error) {
      loginError = 'Network error';
    } finally {
      loading = false;
    }
  }
  
  function logout() {
    isAuthenticated = false;
    token = '';
    password = '';
    localStorage.removeItem('master_token');
  }
  
  async function apiCall(endpoint: string, body?: any) {
    const options: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body || {})
    };

    const response = await fetch(endpoint, options);
    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'API call failed');
    }

    return result.data;
  }
  
  async function loadData() {
    try {
      loading = true;
      dataError = '';
      showSetupMessage = false;

      const [usersData, itemsData, termsData, optionsData, itemMetasData, termMetasData] = await Promise.all([
        apiCall('/api/GetUsers'),
        apiCall('/api/GetItems'),
        apiCall('/api/GetTerms'),
        apiCall('/api/GetOptions'),
        apiCall('/api/GetItemMetas'),
        apiCall('/api/GetTermMetas')
      ]);

      users = usersData.users;
      items = itemsData.items;
      terms = termsData.terms;
      options = optionsData.options;
      itemMetas = itemMetasData.item_metas;
      termMetas = termMetasData.term_metas;
    } catch (error) {
      console.error('Failed to load data:', error);
      dataError = error instanceof Error ? error.message : 'Failed to load data';

      // Check if it's a database setup issue
      if (dataError.includes('does not exist') || dataError.includes('relation') || dataError.includes('table')) {
        showSetupMessage = true;
      }
    } finally {
      loading = false;
    }
  }
  
  async function deleteUser(userId: number) {
    if (confirm('Are you sure you want to delete this user?')) {
      try {
        await apiCall('/api/DeleteUser', { user_id: userId });
        await loadData();
      } catch (error) {
        console.error('Failed to delete user:', error);
        alert('Failed to delete user');
      }
    }
  }
  
  async function deleteItem(itemId: number) {
    if (confirm('Are you sure you want to delete this item?')) {
      try {
        await apiCall('/api/DeleteItem', { item_id: itemId });
        await loadData();
      } catch (error) {
        console.error('Failed to delete item:', error);
        alert('Failed to delete item');
      }
    }
  }

  async function deleteTerm(termId: number) {
    if (confirm('Are you sure you want to delete this term?')) {
      try {
        await apiCall('/api/DeleteTerm', { term_id: termId });
        await loadData();
      } catch (error) {
        console.error('Failed to delete term:', error);
        alert('Failed to delete term');
      }
    }
  }

  async function deleteOption(optionId: number) {
    if (confirm('Are you sure you want to delete this option?')) {
      try {
        await apiCall('/api/DeleteOption', { option_id: optionId });
        await loadData();
      } catch (error) {
        console.error('Failed to delete option:', error);
        alert('Failed to delete option');
      }
    }
  }

  async function deleteItemMeta(itemMetaId: number) {
    if (confirm('Are you sure you want to delete this item meta?')) {
      try {
        await apiCall('/api/DeleteItemMeta', { item_meta_id: itemMetaId });
        await loadData();
      } catch (error) {
        console.error('Failed to delete item meta:', error);
        alert('Failed to delete item meta');
      }
    }
  }

  async function deleteTermMeta(termMetaId: number) {
    if (confirm('Are you sure you want to delete this term meta?')) {
      try {
        await apiCall('/api/DeleteTermMeta', { term_meta_id: termMetaId });
        await loadData();
      } catch (error) {
        console.error('Failed to delete term meta:', error);
        alert('Failed to delete term meta');
      }
    }
  }

  // Add functions
  async function addUser() {
    try {
      await apiCall('/api/SaveUser', { user: newUser });
      newUser = {
        user_firstname: '',
        user_email: '',
        user_created_at: Date.now(),
        user_type: 1
      };
      showAddUserDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to add user:', error);
      alert('Failed to add user');
    }
  }

  async function addItem() {
    try {
      await apiCall('/api/SaveItem', { item: newItem });
      newItem = {
        item_name: '',
        item_slug: '',
        item_status: 0,
        item_created_at: Date.now(),
        item_url: '',
        user_id: undefined
      };
      showAddItemDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to add item:', error);
      alert('Failed to add item');
    }
  }

  async function addTerm() {
    try {
      await apiCall('/api/SaveTerm', { term: newTerm });
      newTerm = {
        term_name: '',
        term_slug: '',
        term_taxonomy: ''
      };
      showAddTermDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to add term:', error);
      alert('Failed to add term');
    }
  }

  async function addOption() {
    try {
      await apiCall('/api/SaveOption', { option: newOption });
      newOption = {
        option_key: '',
        option_value: ''
      };
      showAddOptionDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to add option:', error);
      alert('Failed to add option');
    }
  }

  async function addItemMeta() {
    try {
      await apiCall('/api/SaveItemMeta', { item_meta: newItemMeta });
      newItemMeta = {
        item_id: undefined,
        item_meta_key: '',
        item_meta_value: ''
      };
      showAddItemMetaDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to add item meta:', error);
      alert('Failed to add item meta');
    }
  }

  async function addTermMeta() {
    try {
      await apiCall('/api/SaveTermMeta', { term_meta: newTermMeta });
      newTermMeta = {
        term_id: undefined,
        term_meta_key: '',
        term_meta_value: ''
      };
      showAddTermMetaDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to add term meta:', error);
      alert('Failed to add term meta');
    }
  }

  // Edit functions
  function openEditUser(user: User) {
    editUser = { ...user };
    showEditUserDialog = true;
  }

  function openEditItem(item: Item) {
    editItem = { ...item };
    showEditItemDialog = true;
  }

  function openEditTerm(term: Term) {
    editTerm = { ...term };
    showEditTermDialog = true;
  }

  function openEditOption(option: Option) {
    editOption = { ...option };
    showEditOptionDialog = true;
  }

  function openEditItemMeta(itemMeta: ItemMeta) {
    editItemMeta = { ...itemMeta };
    showEditItemMetaDialog = true;
  }

  function openEditTermMeta(termMeta: TermMeta) {
    editTermMeta = { ...termMeta };
    showEditTermMetaDialog = true;
  }

  // Update functions
  async function updateUser() {
    try {
      await apiCall('/api/SaveUser', { user: editUser });
      showEditUserDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to update user:', error);
      alert('Failed to update user');
    }
  }

  async function updateItem() {
    try {
      await apiCall('/api/SaveItem', { item: editItem });
      showEditItemDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to update item:', error);
      alert('Failed to update item');
    }
  }

  async function updateTerm() {
    try {
      await apiCall('/api/SaveTerm', { term: editTerm });
      showEditTermDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to update term:', error);
      alert('Failed to update term');
    }
  }

  async function updateOption() {
    try {
      await apiCall('/api/SaveOption', { option: editOption });
      showEditOptionDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to update option:', error);
      alert('Failed to update option');
    }
  }

  async function updateItemMeta() {
    try {
      await apiCall('/api/SaveItemMeta', { item_meta: editItemMeta });
      showEditItemMetaDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to update item meta:', error);
      alert('Failed to update item meta');
    }
  }

  async function updateTermMeta() {
    try {
      await apiCall('/api/SaveTermMeta', { term_meta: editTermMeta });
      showEditTermMetaDialog = false;
      await loadData();
    } catch (error) {
      console.error('Failed to update term meta:', error);
      alert('Failed to update term meta');
    }
  }

  // Filtering and pagination functions
  function filterUsers() {
    if (!users || !Array.isArray(users)) return [];
    // Only filter if search query is more than 2 characters
    if (!searchQuery.users || searchQuery.users.length <= 2) return users;
    const query = searchQuery.users.toLowerCase();
    return users.filter(user =>
      user.user_firstname?.toLowerCase().includes(query) ||
      user.user_email?.toLowerCase().includes(query)
    );
  }

  function filterItems() {
    if (!items || !Array.isArray(items)) return [];
    let filtered = items;

    // Filter by search query only if more than 2 characters
    if (searchQuery.items && searchQuery.items.length > 2) {
      const query = searchQuery.items.toLowerCase();
      filtered = filtered.filter(item =>
        item.item_name?.toLowerCase().includes(query) ||
        item.item_slug?.toLowerCase().includes(query) ||
        item.item_url?.toLowerCase().includes(query)
      );
    }

    // Filter by status
    if (itemStatusFilter !== '') {
      filtered = filtered.filter(item => item.item_status?.toString() === itemStatusFilter);
    }

    return filtered;
  }

  function filterTerms() {
    if (!terms || !Array.isArray(terms)) return [];
    // Only filter if search query is more than 2 characters
    if (!searchQuery.terms || searchQuery.terms.length <= 2) return terms;
    const query = searchQuery.terms.toLowerCase();
    return terms.filter(term =>
      term.term_name?.toLowerCase().includes(query) ||
      term.term_slug?.toLowerCase().includes(query) ||
      term.term_taxonomy?.toLowerCase().includes(query)
    );
  }

  function filterOptions() {
    if (!options || !Array.isArray(options)) return [];
    // Only filter if search query is more than 2 characters
    if (!searchQuery.options || searchQuery.options.length <= 2) return options;
    const query = searchQuery.options.toLowerCase();
    return options.filter(option =>
      option.option_key?.toLowerCase().includes(query) ||
      option.option_value?.toLowerCase().includes(query)
    );
  }

  function filterItemMetas() {
    if (!itemMetas || !Array.isArray(itemMetas)) return [];
    // Only filter if search query is more than 2 characters
    if (!searchQuery['item-metas'] || searchQuery['item-metas'].length <= 2) return itemMetas;
    const query = searchQuery['item-metas'].toLowerCase();
    return itemMetas.filter(meta =>
      meta.item_meta_key?.toLowerCase().includes(query) ||
      meta.item_meta_value?.toLowerCase().includes(query)
    );
  }

  function filterTermMetas() {
    if (!termMetas || !Array.isArray(termMetas)) return [];
    // Only filter if search query is more than 2 characters
    if (!searchQuery['term-metas'] || searchQuery['term-metas'].length <= 2) return termMetas;
    const query = searchQuery['term-metas'].toLowerCase();
    return termMetas.filter(meta =>
      meta.term_meta_key?.toLowerCase().includes(query) ||
      meta.term_meta_value?.toLowerCase().includes(query)
    );
  }

  function paginateData(data: any[], tab: string) {
    if (!data || !Array.isArray(data)) return [];
    const page = currentPage[tab as keyof typeof currentPage];
    const start = (page - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    return data.slice(start, end);
  }

  function getTotalPages(dataLength: number) {
    return Math.ceil(dataLength / itemsPerPage);
  }

  function changePage(tab: string, page: number) {
    currentPage = { ...currentPage, [tab]: page };
  }

  function switchTab(newTab: string) {
    activeTab = newTab;
    // Reset to page 1 when switching tabs if current page doesn't exist
    const filteredData = getFilteredDataForTab(newTab);
    const totalPages = getTotalPages(filteredData.length);
    if (currentPage[newTab as keyof typeof currentPage] > totalPages && totalPages > 0) {
      currentPage = { ...currentPage, [newTab]: 1 };
    }
  }

  function getFilteredDataForTab(tab: string) {
    switch (tab) {
      case 'users': return filteredUsers || [];
      case 'items': return filteredItems || [];
      case 'terms': return filteredTerms || [];
      case 'options': return filteredOptions || [];
      case 'item-metas': return filteredItemMetas || [];
      case 'term-metas': return filteredTermMetas || [];
      default: return [];
    }
  }

  // Reactive statements for filtered and paginated data
  $: filteredUsers = users ? filterUsers() : [];
  $: paginatedUsers = filteredUsers ? paginateData(filteredUsers, 'users') : [];
  $: usersTotalPages = filteredUsers ? getTotalPages(filteredUsers.length) : 0;

  $: filteredItems = items ? filterItems() : [];
  $: paginatedItems = filteredItems ? paginateData(filteredItems, 'items') : [];
  $: itemsTotalPages = filteredItems ? getTotalPages(filteredItems.length) : 0;

  $: filteredTerms = terms ? filterTerms() : [];
  $: paginatedTerms = filteredTerms ? paginateData(filteredTerms, 'terms') : [];
  $: termsTotalPages = filteredTerms ? getTotalPages(filteredTerms.length) : 0;

  $: filteredOptions = options ? filterOptions() : [];
  $: paginatedOptions = filteredOptions ? paginateData(filteredOptions, 'options') : [];
  $: optionsTotalPages = filteredOptions ? getTotalPages(filteredOptions.length) : 0;

  $: filteredItemMetas = itemMetas ? filterItemMetas() : [];
  $: paginatedItemMetas = filteredItemMetas ? paginateData(filteredItemMetas, 'item-metas') : [];
  $: itemMetasTotalPages = filteredItemMetas ? getTotalPages(filteredItemMetas.length) : 0;

  $: filteredTermMetas = termMetas ? filterTermMetas() : [];
  $: paginatedTermMetas = filteredTermMetas ? paginateData(filteredTermMetas, 'term-metas') : [];
  $: termMetasTotalPages = filteredTermMetas ? getTotalPages(filteredTermMetas.length) : 0;
</script>

{#if !isAuthenticated}
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="w-full max-w-md bg-white rounded-lg shadow-md">
      <div class="p-6 border-b border-gray-100">
        <h1 class="text-xl font-semibold text-gray-900">Master Dashboard Login</h1>
        <p class="text-sm text-gray-600 mt-1">Enter the master password to access the dashboard</p>
      </div>
      <div class="p-6 space-y-4">
        <input
          type="password"
          placeholder="Master password"
          bind:value={password}
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        {#if loginError}
          <p class="text-sm text-red-600">{loginError}</p>
        {/if}
        <button
          onclick={login}
          disabled={loading || !password}
          class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md transition-colors disabled:cursor-not-allowed"
        >
          {loading ? 'Logging in...' : 'Login'}
        </button>
      </div>
    </div>
  </div>
{:else}
  <div class="min-h-screen bg-gray-50">
    <header class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <h1 class="text-3xl md:text-4xl font-bold text-blue-900">Master Dashboard</h1>
          <button onclick={logout} class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium">
            Logout
          </button>
        </div>
      </div>
    </header>
    
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {#if showSetupMessage}
        <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">Database Setup Required</h3>
              <div class="mt-2 text-sm text-yellow-700">
                <p>The database tables don't exist yet. You need to set up the database first.</p>
                <div class="mt-2">
                  <a href="/setup" class="font-medium text-yellow-800 underline hover:text-yellow-600">
                    Go to Database Setup →
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      {:else if dataError}
        <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Error Loading Data</h3>
              <div class="mt-2 text-sm text-red-700">
                <p>{dataError}</p>
                <button
                  onclick={loadData}
                  class="mt-2 font-medium text-red-800 underline hover:text-red-600 p-0 bg-transparent border-none cursor-pointer"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      {/if}

      <!-- Custom Tabs Implementation -->
      <div class="w-full">
        <div class="grid w-full grid-cols-6 bg-gray-100 rounded-lg p-1 mb-6">
          <button
            onclick={() => switchTab('users')}
            class="px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === 'users' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
          >
            Users ({users?.length || 0})
          </button>
          <button
            onclick={() => switchTab('items')}
            class="px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === 'items' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
          >
            Items ({items?.length || 0})
          </button>
          <button
            onclick={() => switchTab('terms')}
            class="px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === 'terms' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
          >
            Terms ({terms?.length || 0})
          </button>
          <button
            onclick={() => switchTab('options')}
            class="px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === 'options' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
          >
            Options ({options?.length || 0})
          </button>
          <button
            onclick={() => switchTab('item-metas')}
            class="px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === 'item-metas' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
          >
            Item Metas ({itemMetas?.length || 0})
          </button>
          <button
            onclick={() => switchTab('term-metas')}
            class="px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === 'term-metas' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
          >
            Term Metas ({termMetas?.length || 0})
          </button>
        </div>

        <!-- Users Tab Content -->
        {#if activeTab === 'users'}
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h2 class="text-xl font-semibold">Users Management</h2>
              <button
                onclick={() => showAddUserDialog = true}
                class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 transition-colors text-sm font-medium"
              >
                Add New User
              </button>
            </div>

            <!-- Search Box -->
            <div class="flex gap-4 items-center">
              <div class="flex-1">
                <input
                  type="text"
                  placeholder="Search users by name or email (min 3 characters)..."
                  bind:value={searchQuery.users}
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div class="text-sm text-gray-600">
                {searchQuery.users && searchQuery.users.length > 2 ? `${filteredUsers?.length || 0} of ${users?.length || 0}` : users?.length || 0} users
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {#each paginatedUsers as user}
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
                  <div class="p-6">
                    <div class="space-y-3">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <h3 class="font-semibold text-lg text-gray-900 mb-1">{user.user_firstname}</h3>
                          <p class="text-sm text-gray-600 break-all">{user.user_email}</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          ID: {user.user_id}
                        </span>
                      </div>

                      <div class="space-y-1">
                        <div class="flex items-center gap-2">
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {user.user_type === 0 ? 'Inactive' : user.user_type === 1 ? 'Member' : 'Admin'}
                          </span>
                        </div>
                        <p class="text-xs text-gray-500">
                          Created: {new Date(Number(user.user_created_at)).toLocaleDateString()}
                        </p>
                      </div>

                      <div class="flex gap-2 pt-2">
                        <button
                          onclick={() => openEditUser(user)}
                          class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
                        >
                          Edit
                        </button>
                        <button
                          onclick={() => deleteUser(user.user_id!)}
                          class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              {/each}
            </div>

            <!-- Pagination -->
            {#if usersTotalPages > 1}
              <div class="flex justify-center items-center gap-4 mt-6">
                <button
                  onclick={() => changePage('users', currentPage.users - 1)}
                  disabled={currentPage.users === 1}
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  Previous
                </button>

                <span class="text-gray-600">
                  Page {currentPage.users} of {usersTotalPages}
                </span>

                <button
                  onclick={() => changePage('users', currentPage.users + 1)}
                  disabled={currentPage.users === usersTotalPages}
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  Next
                </button>
              </div>
            {/if}
          </div>
        {/if}

        <!-- Items Tab Content -->
        {#if activeTab === 'items'}
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h2 class="text-xl font-semibold">Items Management</h2>
              <button
                onclick={() => showAddItemDialog = true}
                disabled={!users || users.length === 0}
                title={!users || users.length === 0 ? "Create a user first" : ""}
                class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
              >
                Add New Item
              </button>
            </div>

            <!-- Search and Filter Box -->
            <div class="flex gap-4 items-center">
              <div class="flex-1">
                <input
                  type="text"
                  placeholder="Search items by name, slug, or URL (min 3 characters)..."
                  bind:value={searchQuery.items}
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div class="flex gap-2 items-center">
                <label for="item_status_filter" class="text-sm font-medium text-gray-700">Status:</label>
                <select
                  id="item_status_filter"
                  bind:value={itemStatusFilter}
                  class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All</option>
                  <option value="0">Pending (0)</option>
                  <option value="1">Active (1)</option>
                  <option value="2">Featured (2)</option>
                </select>
              </div>
              <div class="text-sm text-gray-600">
                {(searchQuery.items && searchQuery.items.length > 2) || itemStatusFilter ? `${filteredItems?.length || 0} of ${items?.length || 0}` : items?.length || 0} items
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {#each paginatedItems as item}
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
                  <div class="p-6">
                    <div class="space-y-3">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <h3 class="font-semibold text-lg text-gray-900 mb-1">{item.item_name}</h3>
                          <p class="text-sm text-gray-600">Slug: {item.item_slug}</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          ID: {item.item_id}
                        </span>
                      </div>

                      <div class="space-y-1">
                        <p class="text-sm text-blue-600 break-all">{item.item_url || 'No URL'}</p>
                        <div class="flex items-center gap-2">
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {item.item_status === 1 ? 'bg-green-100 text-green-800' : item.item_status === 2 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}">
                            {item.item_status === 0 ? 'Pending' : item.item_status === 1 ? 'Active' : 'Featured'}
                          </span>
                        </div>
                        <p class="text-xs text-gray-500">User ID: {item.user_id}</p>
                        <p class="text-xs text-gray-500">Created: {new Date(Number(item.item_created_at)).toLocaleDateString()}</p>
                      </div>

                      <div class="flex gap-2 pt-2">
                        <button
                          onclick={() => openEditItem(item)}
                          class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
                        >
                          Edit
                        </button>
                        <button
                          onclick={() => deleteItem(item.item_id!)}
                          class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              {/each}
            </div>

            <!-- Pagination -->
            {#if itemsTotalPages > 1}
              <div class="flex justify-center items-center gap-4 mt-6">
                <button
                  onclick={() => changePage('items', currentPage.items - 1)}
                  disabled={currentPage.items === 1}
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  Previous
                </button>

                <span class="text-gray-600">
                  Page {currentPage.items} of {itemsTotalPages}
                </span>

                <button
                  onclick={() => changePage('items', currentPage.items + 1)}
                  disabled={currentPage.items === itemsTotalPages}
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  Next
                </button>
              </div>
            {/if}
          </div>
        {/if}

        <!-- Terms Tab Content -->
        {#if activeTab === 'terms'}
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h2 class="text-xl font-semibold">Terms Management</h2>
              <button
                onclick={() => showAddTermDialog = true}
                class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 transition-colors text-sm font-medium"
              >
                Add New Term
              </button>
            </div>

            <!-- Search Box -->
            <div class="flex gap-4 items-center">
              <div class="flex-1">
                <input
                  type="text"
                  placeholder="Search terms by name, slug, or taxonomy (min 3 characters)..."
                  bind:value={searchQuery.terms}
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div class="text-sm text-gray-600">
                {searchQuery.terms && searchQuery.terms.length > 2 ? `${filteredTerms?.length || 0} of ${terms?.length || 0}` : terms?.length || 0} terms
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {#each paginatedTerms as term}
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
                  <div class="p-6">
                    <div class="space-y-3">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <h3 class="font-semibold text-lg text-gray-900 mb-1">{term.term_name}</h3>
                          <p class="text-sm text-gray-600">Slug: {term.term_slug}</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          ID: {term.term_id}
                        </span>
                      </div>

                      <div class="space-y-1">
                        <div class="flex items-center gap-2">
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            {term.term_taxonomy}
                          </span>
                        </div>
                      </div>

                      <div class="flex gap-2 pt-2">
                        <button
                          onclick={() => openEditTerm(term)}
                          class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
                        >
                          Edit
                        </button>
                        <button
                          onclick={() => deleteTerm(term.term_id!)}
                          class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              {/each}
            </div>

            <!-- Pagination -->
            {#if termsTotalPages > 1}
              <div class="flex justify-center items-center gap-4 mt-6">
                <button
                  onclick={() => changePage('terms', currentPage.terms - 1)}
                  disabled={currentPage.terms === 1}
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  Previous
                </button>

                <span class="text-gray-600">
                  Page {currentPage.terms} of {termsTotalPages}
                </span>

                <button
                  onclick={() => changePage('terms', currentPage.terms + 1)}
                  disabled={currentPage.terms === termsTotalPages}
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  Next
                </button>
              </div>
            {/if}
          </div>
        {/if}

        <!-- Options Tab Content -->
        {#if activeTab === 'options'}
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h2 class="text-xl font-semibold">Options Management</h2>
              <button
                onclick={() => showAddOptionDialog = true}
                class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 transition-colors text-sm font-medium"
              >
                Add New Option
              </button>
            </div>

            <!-- Search Box -->
            <div class="flex gap-4 items-center">
              <div class="flex-1">
                <input
                  type="text"
                  placeholder="Search options by key or value (min 3 characters)..."
                  bind:value={searchQuery.options}
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div class="text-sm text-gray-600">
                {searchQuery.options && searchQuery.options.length > 2 ? `${filteredOptions?.length || 0} of ${options?.length || 0}` : options?.length || 0} options
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {#each paginatedOptions as option}
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
                  <div class="p-6">
                    <div class="space-y-3">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <h3 class="font-semibold text-lg text-gray-900 mb-1">{option.option_key}</h3>
                          <p class="text-sm text-gray-600 break-all">{option.option_value || 'No value'}</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          ID: {option.option_id}
                        </span>
                      </div>

                      <div class="flex gap-2 pt-2">
                        <button
                          onclick={() => openEditOption(option)}
                          class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
                        >
                          Edit
                        </button>
                        <button
                          onclick={() => deleteOption(option.option_id!)}
                          class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              {/each}
            </div>

            <!-- Pagination -->
            {#if optionsTotalPages > 1}
              <div class="flex justify-center items-center gap-4 mt-6">
                <button
                  onclick={() => changePage('options', currentPage.options - 1)}
                  disabled={currentPage.options === 1}
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  Previous
                </button>

                <span class="text-gray-600">
                  Page {currentPage.options} of {optionsTotalPages}
                </span>

                <button
                  onclick={() => changePage('options', currentPage.options + 1)}
                  disabled={currentPage.options === optionsTotalPages}
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  Next
                </button>
              </div>
            {/if}
          </div>
        {/if}

        <!-- Item Metas Tab Content -->
        {#if activeTab === 'item-metas'}
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h2 class="text-xl font-semibold">Item Metas Management</h2>
              <button
                onclick={() => showAddItemMetaDialog = true}
                disabled={!items || items.length === 0}
                title={!items || items.length === 0 ? "Create an item first before adding item metas" : ""}
                class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
              >
                Add New Item Meta
              </button>
            </div>

            <!-- Search Box -->
            <div class="flex gap-4 items-center">
              <div class="flex-1">
                <input
                  type="text"
                  placeholder="Search item metas by key or value (min 3 characters)..."
                  bind:value={searchQuery['item-metas']}
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div class="text-sm text-gray-600">
                {searchQuery['item-metas'] && searchQuery['item-metas'].length > 2 ? `${filteredItemMetas?.length || 0} of ${itemMetas?.length || 0}` : itemMetas?.length || 0} item metas
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {#each paginatedItemMetas as itemMeta}
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
                  <div class="p-6">
                    <div class="space-y-3">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <h3 class="font-semibold text-lg text-gray-900 mb-1">{itemMeta.item_meta_key}</h3>
                          <p class="text-sm text-gray-600 break-all">{itemMeta.item_meta_value || 'No value'}</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          ID: {itemMeta.item_meta_id}
                        </span>
                      </div>

                      <div class="space-y-1">
                        <p class="text-xs text-gray-500">Item ID: {itemMeta.item_id}</p>
                      </div>

                      <div class="flex gap-2 pt-2">
                        <button
                          onclick={() => openEditItemMeta(itemMeta)}
                          class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
                        >
                          Edit
                        </button>
                        <button
                          onclick={() => deleteItemMeta(itemMeta.item_meta_id!)}
                          class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              {/each}
            </div>

            <!-- Pagination -->
            {#if itemMetasTotalPages > 1}
              <div class="flex justify-center items-center gap-4 mt-6">
                <button
                  onclick={() => changePage('item-metas', currentPage['item-metas'] - 1)}
                  disabled={currentPage['item-metas'] === 1}
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  Previous
                </button>

                <span class="text-gray-600">
                  Page {currentPage['item-metas']} of {itemMetasTotalPages}
                </span>

                <button
                  onclick={() => changePage('item-metas', currentPage['item-metas'] + 1)}
                  disabled={currentPage['item-metas'] === itemMetasTotalPages}
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  Next
                </button>
              </div>
            {/if}
          </div>
        {/if}

        <!-- Term Metas Tab Content -->
        {#if activeTab === 'term-metas'}
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h2 class="text-xl font-semibold">Term Metas Management</h2>
              <button
                onclick={() => showAddTermMetaDialog = true}
                disabled={terms.length === 0}
                title={terms.length === 0 ? "Create a term first before adding term metas" : ""}
                class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
              >
                Add New Term Meta
              </button>
            </div>

            <!-- Search Box -->
            <div class="flex gap-4 items-center">
              <div class="flex-1">
                <input
                  type="text"
                  placeholder="Search term metas by key or value (min 3 characters)..."
                  bind:value={searchQuery['term-metas']}
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div class="text-sm text-gray-600">
                {searchQuery['term-metas'] && searchQuery['term-metas'].length > 2 ? `${filteredTermMetas?.length || 0} of ${termMetas?.length || 0}` : termMetas?.length || 0} term metas
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {#each paginatedTermMetas as termMeta}
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
                  <div class="p-6">
                    <div class="space-y-3">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <h3 class="font-semibold text-lg text-gray-900 mb-1">{termMeta.term_meta_key}</h3>
                          <p class="text-sm text-gray-600 break-all">{termMeta.term_meta_value || 'No value'}</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          ID: {termMeta.term_meta_id}
                        </span>
                      </div>

                      <div class="space-y-1">
                        <p class="text-xs text-gray-500">Term ID: {termMeta.term_id}</p>
                      </div>

                      <div class="flex gap-2 pt-2">
                        <button
                          onclick={() => openEditTermMeta(termMeta)}
                          class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
                        >
                          Edit
                        </button>
                        <button
                          onclick={() => deleteTermMeta(termMeta.term_meta_id!)}
                          class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              {/each}
            </div>

            <!-- Pagination -->
            {#if termMetasTotalPages > 1}
              <div class="flex justify-center items-center gap-4 mt-6">
                <button
                  onclick={() => changePage('term-metas', currentPage['term-metas'] - 1)}
                  disabled={currentPage['term-metas'] === 1}
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  Previous
                </button>

                <span class="text-gray-600">
                  Page {currentPage['term-metas']} of {termMetasTotalPages}
                </span>

                <button
                  onclick={() => changePage('term-metas', currentPage['term-metas'] + 1)}
                  disabled={currentPage['term-metas'] === termMetasTotalPages}
                  class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  Next
                </button>
              </div>
            {/if}
          </div>
        {/if}
      </div>
    </main>
  </div>

  <!-- Add User Dialog -->
  {#if showAddUserDialog}
    <div
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      role="dialog"
      aria-modal="true"
      tabindex="-1"
      onclick={() => showAddUserDialog = false}
      onkeydown={(e) => e.key === 'Escape' && (showAddUserDialog = false)}
    >
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Add New User</h3>
          <p class="text-sm text-gray-600 mt-1">Create a new user in the system.</p>
        </div>
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="user_firstname" class="text-right text-sm font-medium">Name</label>
            <input
              id="user_firstname"
              type="text"
              bind:value={newUser.user_firstname}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="First name"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="user_email" class="text-right text-sm font-medium">Email</label>
            <input
              id="user_email"
              type="email"
              bind:value={newUser.user_email}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="<EMAIL>"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="user_type" class="text-right text-sm font-medium">Type</label>
            <select
              id="user_type"
              bind:value={newUser.user_type}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={0}>Inactive</option>
              <option value={1}>Member</option>
              <option value={2}>Admin</option>
            </select>
          </div>
        </div>
        <div class="p-6 border-t border-gray-200 flex justify-end gap-2">
          <button
            onclick={() => showAddUserDialog = false}
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onclick={addUser}
            disabled={!newUser.user_firstname || !newUser.user_email}
            class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
          >
            Add User
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Add Item Dialog -->
  {#if showAddItemDialog}
    <div
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      role="dialog"
      aria-modal="true"
      tabindex="-1"
      onclick={() => showAddItemDialog = false}
      onkeydown={(e) => e.key === 'Escape' && (showAddItemDialog = false)}
    >
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Add New Item</h3>
          <p class="text-sm text-gray-600 mt-1">Create a new item in the system.</p>
        </div>
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="item_name" class="text-right text-sm font-medium">Name</label>
            <input
              id="item_name"
              type="text"
              bind:value={newItem.item_name}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Item name"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="item_slug" class="text-right text-sm font-medium">Slug</label>
            <input
              id="item_slug"
              type="text"
              bind:value={newItem.item_slug}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="item-slug"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="item_url" class="text-right text-sm font-medium">URL</label>
            <input
              id="item_url"
              type="text"
              bind:value={newItem.item_url}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="https://example.com"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="item_status" class="text-right text-sm font-medium">Status</label>
            <select
              id="item_status"
              bind:value={newItem.item_status}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={0}>Pending</option>
              <option value={1}>Active</option>
              <option value={2}>Featured</option>
            </select>
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="item_user_id" class="text-right text-sm font-medium">User</label>
            <select
              id="item_user_id"
              bind:value={newItem.user_id}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select a user (required)</option>
              {#each users as user}
                <option value={user.user_id}>{user.user_firstname} ({user.user_email})</option>
              {/each}
            </select>
          </div>
        </div>
        <div class="p-6 border-t border-gray-200 flex justify-end gap-2">
          <button
            onclick={() => showAddItemDialog = false}
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onclick={addItem}
            disabled={!newItem.item_name || !newItem.item_slug || !newItem.user_id}
            class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
          >
            Add Item
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Add Term Dialog -->
  {#if showAddTermDialog}
    <div
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      role="dialog"
      aria-modal="true"
      tabindex="-1"
      onclick={() => showAddTermDialog = false}
      onkeydown={(e) => e.key === 'Escape' && (showAddTermDialog = false)}
    >
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Add New Term</h3>
          <p class="text-sm text-gray-600 mt-1">Create a new term in the system.</p>
        </div>
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="term_name" class="text-right text-sm font-medium">Name</label>
            <input
              id="term_name"
              type="text"
              bind:value={newTerm.term_name}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Term name"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="term_slug" class="text-right text-sm font-medium">Slug</label>
            <input
              id="term_slug"
              type="text"
              bind:value={newTerm.term_slug}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="term-slug"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="term_taxonomy" class="text-right text-sm font-medium">Taxonomy</label>
            <input
              id="term_taxonomy"
              type="text"
              bind:value={newTerm.term_taxonomy}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="category, tag, etc."
            />
          </div>
        </div>
        <div class="p-6 border-t border-gray-200 flex justify-end gap-2">
          <button
            onclick={() => showAddTermDialog = false}
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onclick={addTerm}
            disabled={!newTerm.term_name || !newTerm.term_slug || !newTerm.term_taxonomy}
            class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
          >
            Add Term
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Add Option Dialog -->
  {#if showAddOptionDialog}
    <div
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      role="dialog"
      aria-modal="true"
      tabindex="-1"
      onclick={() => showAddOptionDialog = false}
      onkeydown={(e) => e.key === 'Escape' && (showAddOptionDialog = false)}
    >
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Add New Option</h3>
          <p class="text-sm text-gray-600 mt-1">Create a new option in the system.</p>
        </div>
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="option_key" class="text-right text-sm font-medium">Key</label>
            <input
              id="option_key"
              type="text"
              bind:value={newOption.option_key}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="option_key"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="option_value" class="text-right text-sm font-medium">Value</label>
            <input
              id="option_value"
              type="text"
              bind:value={newOption.option_value}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Option value"
            />
          </div>
        </div>
        <div class="p-6 border-t border-gray-200 flex justify-end gap-2">
          <button
            onclick={() => showAddOptionDialog = false}
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onclick={addOption}
            disabled={!newOption.option_key || !newOption.option_value}
            class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
          >
            Add Option
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Add Item Meta Dialog -->
  {#if showAddItemMetaDialog}
    <div
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      role="dialog"
      aria-modal="true"
      tabindex="-1"
      onclick={() => showAddItemMetaDialog = false}
      onkeydown={(e) => e.key === 'Escape' && (showAddItemMetaDialog = false)}
    >
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Add New Item Meta</h3>
          <p class="text-sm text-gray-600 mt-1">Create a new item meta in the system.</p>
        </div>
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="item_meta_item_id" class="text-right text-sm font-medium">Item</label>
            <select
              id="item_meta_item_id"
              bind:value={newItemMeta.item_id}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select an item (required)</option>
              {#each items as item}
                <option value={item.item_id}>{item.item_name} (ID: {item.item_id})</option>
              {/each}
            </select>
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="item_meta_key" class="text-right text-sm font-medium">Key</label>
            <input
              id="item_meta_key"
              type="text"
              bind:value={newItemMeta.item_meta_key}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="meta_key"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="item_meta_value" class="text-right text-sm font-medium">Value</label>
            <input
              id="item_meta_value"
              type="text"
              bind:value={newItemMeta.item_meta_value}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Meta value"
            />
          </div>
        </div>
        <div class="p-6 border-t border-gray-200 flex justify-end gap-2">
          <button
            onclick={() => showAddItemMetaDialog = false}
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onclick={addItemMeta}
            disabled={!newItemMeta.item_meta_key || !newItemMeta.item_id}
            class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
          >
            Add Item Meta
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Add Term Meta Dialog -->
  {#if showAddTermMetaDialog}
    <div
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      role="dialog"
      aria-modal="true"
      tabindex="-1"
      onclick={() => showAddTermMetaDialog = false}
      onkeydown={(e) => e.key === 'Escape' && (showAddTermMetaDialog = false)}
    >
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Add New Term Meta</h3>
          <p class="text-sm text-gray-600 mt-1">Create a new term meta in the system.</p>
        </div>
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="term_meta_term_id" class="text-right text-sm font-medium">Term</label>
            <select
              id="term_meta_term_id"
              bind:value={newTermMeta.term_id}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select a term (required)</option>
              {#each terms as term}
                <option value={term.term_id}>{term.term_name} ({term.term_taxonomy}) - ID: {term.term_id}</option>
              {/each}
            </select>
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="term_meta_key" class="text-right text-sm font-medium">Key</label>
            <input
              id="term_meta_key"
              type="text"
              bind:value={newTermMeta.term_meta_key}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="meta_key"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="term_meta_value" class="text-right text-sm font-medium">Value</label>
            <input
              id="term_meta_value"
              type="text"
              bind:value={newTermMeta.term_meta_value}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Meta value"
            />
          </div>
        </div>
        <div class="p-6 border-t border-gray-200 flex justify-end gap-2">
          <button
            onclick={() => showAddTermMetaDialog = false}
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onclick={addTermMeta}
            disabled={!newTermMeta.term_meta_key || !newTermMeta.term_id}
            class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
          >
            Add Term Meta
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Edit User Dialog -->
  <Dialog.Root bind:open={showEditUserDialog}>
    <Dialog.Portal>
      <Dialog.Overlay />
      <Dialog.Content class="max-w-md">
        <Dialog.Header>
          <Dialog.Title>Edit User</Dialog.Title>
          <Dialog.Description>Update user information.</Dialog.Description>
        </Dialog.Header>
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_user_firstname" class="text-right text-sm font-medium">Name</label>
            <input
              id="edit_user_firstname"
              type="text"
              bind:value={editUser.user_firstname}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="First name"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_user_email" class="text-right text-sm font-medium">Email</label>
            <input
              id="edit_user_email"
              type="email"
              bind:value={editUser.user_email}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="<EMAIL>"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_user_type" class="text-right text-sm font-medium">Type</label>
            <select
              id="edit_user_type"
              bind:value={editUser.user_type}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={0}>Inactive</option>
              <option value={1}>Member</option>
              <option value={2}>Admin</option>
            </select>
          </div>
        </div>
        <Dialog.Footer>
          <button
            onclick={() => showEditUserDialog = false}
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onclick={updateUser}
            disabled={!editUser.user_firstname || !editUser.user_email}
            class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
          >
            Update User
          </button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Portal>
  </Dialog.Root>

  <!-- Edit Item Dialog -->
  <Dialog.Root bind:open={showEditItemDialog}>
    <Dialog.Portal>
      <Dialog.Overlay />
      <Dialog.Content class="max-w-md">
        <Dialog.Header>
          <Dialog.Title>Edit Item</Dialog.Title>
          <Dialog.Description>Update item information.</Dialog.Description>
        </Dialog.Header>
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_item_name" class="text-right text-sm font-medium">Name</label>
            <input
              id="edit_item_name"
              type="text"
              bind:value={editItem.item_name}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Item name"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_item_slug" class="text-right text-sm font-medium">Slug</label>
            <input
              id="edit_item_slug"
              type="text"
              bind:value={editItem.item_slug}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="item-slug"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_item_url" class="text-right text-sm font-medium">URL</label>
            <input
              id="edit_item_url"
              type="text"
              bind:value={editItem.item_url}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="https://example.com"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_item_status" class="text-right text-sm font-medium">Status</label>
            <select
              id="edit_item_status"
              bind:value={editItem.item_status}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={0}>Pending</option>
              <option value={1}>Active</option>
              <option value={2}>Featured</option>
            </select>
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_item_user_id" class="text-right text-sm font-medium">User</label>
            <select
              id="edit_item_user_id"
              bind:value={editItem.user_id}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select a user (required)</option>
              {#each users as user}
                <option value={user.user_id}>{user.user_firstname} ({user.user_email})</option>
              {/each}
            </select>
          </div>
        </div>
        <Dialog.Footer>
          <button
            onclick={() => showEditItemDialog = false}
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onclick={updateItem}
            disabled={!editItem.item_name || !editItem.item_slug || !editItem.user_id}
            class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
          >
            Update Item
          </button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Portal>
  </Dialog.Root>

  <!-- Edit Term Dialog -->
  <Dialog.Root bind:open={showEditTermDialog}>
    <Dialog.Portal>
      <Dialog.Overlay />
      <Dialog.Content class="max-w-md">
        <Dialog.Header>
          <Dialog.Title>Edit Term</Dialog.Title>
          <Dialog.Description>Update term information.</Dialog.Description>
        </Dialog.Header>
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_term_name" class="text-right text-sm font-medium">Name</label>
            <input
              id="edit_term_name"
              type="text"
              bind:value={editTerm.term_name}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Term name"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_term_slug" class="text-right text-sm font-medium">Slug</label>
            <input
              id="edit_term_slug"
              type="text"
              bind:value={editTerm.term_slug}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="term-slug"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_term_taxonomy" class="text-right text-sm font-medium">Taxonomy</label>
            <input
              id="edit_term_taxonomy"
              type="text"
              bind:value={editTerm.term_taxonomy}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="category, tag, etc."
            />
          </div>
        </div>
        <Dialog.Footer>
          <button
            onclick={() => showEditTermDialog = false}
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onclick={updateTerm}
            disabled={!editTerm.term_name || !editTerm.term_slug || !editTerm.term_taxonomy}
            class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
          >
            Update Term
          </button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Portal>
  </Dialog.Root>

  <!-- Edit Option Dialog -->
  <Dialog.Root bind:open={showEditOptionDialog}>
    <Dialog.Portal>
      <Dialog.Overlay />
      <Dialog.Content class="max-w-md">
        <Dialog.Header>
          <Dialog.Title>Edit Option</Dialog.Title>
          <Dialog.Description>Update option information.</Dialog.Description>
        </Dialog.Header>
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_option_key" class="text-right text-sm font-medium">Key</label>
            <input
              id="edit_option_key"
              type="text"
              bind:value={editOption.option_key}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="option_key"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_option_value" class="text-right text-sm font-medium">Value</label>
            <input
              id="edit_option_value"
              type="text"
              bind:value={editOption.option_value}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Option value"
            />
          </div>
        </div>
        <Dialog.Footer>
          <button
            onclick={() => showEditOptionDialog = false}
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onclick={updateOption}
            disabled={!editOption.option_key || !editOption.option_value}
            class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
          >
            Update Option
          </button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Portal>
  </Dialog.Root>

  <!-- Edit Item Meta Dialog -->
  <Dialog.Root bind:open={showEditItemMetaDialog}>
    <Dialog.Portal>
      <Dialog.Overlay />
      <Dialog.Content class="max-w-md">
        <Dialog.Header>
          <Dialog.Title>Edit Item Meta</Dialog.Title>
          <Dialog.Description>Update item meta information.</Dialog.Description>
        </Dialog.Header>
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_item_meta_item_id" class="text-right text-sm font-medium">Item</label>
            <select
              id="edit_item_meta_item_id"
              bind:value={editItemMeta.item_id}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select an item (required)</option>
              {#each items as item}
                <option value={item.item_id}>{item.item_name} (ID: {item.item_id})</option>
              {/each}
            </select>
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_item_meta_key" class="text-right text-sm font-medium">Key</label>
            <input
              id="edit_item_meta_key"
              type="text"
              bind:value={editItemMeta.item_meta_key}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="meta_key"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_item_meta_value" class="text-right text-sm font-medium">Value</label>
            <input
              id="edit_item_meta_value"
              type="text"
              bind:value={editItemMeta.item_meta_value}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Meta value"
            />
          </div>
        </div>
        <Dialog.Footer>
          <button
            onclick={() => showEditItemMetaDialog = false}
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onclick={updateItemMeta}
            disabled={!editItemMeta.item_meta_key || !editItemMeta.item_id}
            class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
          >
            Update Item Meta
          </button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Portal>
  </Dialog.Root>

  <!-- Edit Term Meta Dialog -->
  <Dialog.Root bind:open={showEditTermMetaDialog}>
    <Dialog.Portal>
      <Dialog.Overlay />
      <Dialog.Content class="max-w-md">
        <Dialog.Header>
          <Dialog.Title>Edit Term Meta</Dialog.Title>
          <Dialog.Description>Update term meta information.</Dialog.Description>
        </Dialog.Header>
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_term_meta_term_id" class="text-right text-sm font-medium">Term</label>
            <select
              id="edit_term_meta_term_id"
              bind:value={editTermMeta.term_id}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select a term (required)</option>
              {#each terms as term}
                <option value={term.term_id}>{term.term_name} ({term.term_taxonomy}) - ID: {term.term_id}</option>
              {/each}
            </select>
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_term_meta_key" class="text-right text-sm font-medium">Key</label>
            <input
              id="edit_term_meta_key"
              type="text"
              bind:value={editTermMeta.term_meta_key}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="meta_key"
            />
          </div>
          <div class="grid grid-cols-4 items-center gap-4">
            <label for="edit_term_meta_value" class="text-right text-sm font-medium">Value</label>
            <input
              id="edit_term_meta_value"
              type="text"
              bind:value={editTermMeta.term_meta_value}
              class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Meta value"
            />
          </div>
        </div>
        <Dialog.Footer>
          <button
            onclick={() => showEditTermMetaDialog = false}
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Cancel
          </button>
          <button
            onclick={updateTermMeta}
            disabled={!editTermMeta.term_meta_key || !editTermMeta.term_id}
            class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
          >
            Update Term Meta
          </button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Portal>
  </Dialog.Root>
{/if}
